[gd_resource type="Theme" load_steps=15 format=3 uid="uid://mobile_theme"]

[ext_resource type="FontFile" path="res://fonts/<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf" id="1_font"]
[ext_resource type="Texture2D" uid="uid://h4fgxwlt7858" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Normal.png" id="2_button_normal"]
[ext_resource type="Texture2D" uid="uid://6hx86f2x1kth" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Hover.png" id="3_button_hover"]
[ext_resource type="Texture2D" uid="uid://x0ltngktugqp" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Sliders/Panel.png" id="4_slider_bg"]
[ext_resource type="Texture2D" uid="uid://b8ccvw6f1cu2r" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Sliders/Slider01.png" id="5_slider_grabber"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_normal"]
texture = ExtResource("2_button_normal")
texture_margin_left = 25.0
texture_margin_top = 25.0
texture_margin_right = 25.0
texture_margin_bottom = 25.0
expand_margin_left = 5.0
expand_margin_top = 5.0
expand_margin_right = 5.0
expand_margin_bottom = 5.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_hover"]
texture = ExtResource("3_button_hover")
texture_margin_left = 25.0
texture_margin_top = 25.0
texture_margin_right = 25.0
texture_margin_bottom = 25.0
expand_margin_left = 5.0
expand_margin_top = 5.0
expand_margin_right = 5.0
expand_margin_bottom = 5.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_pressed"]
texture = ExtResource("2_button_normal")
texture_margin_left = 25.0
texture_margin_top = 25.0
texture_margin_right = 25.0
texture_margin_bottom = 25.0
expand_margin_left = 3.0
expand_margin_top = 3.0
expand_margin_right = 3.0
expand_margin_bottom = 3.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_disabled"]
texture = ExtResource("2_button_normal")
texture_margin_left = 25.0
texture_margin_top = 25.0
texture_margin_right = 25.0
texture_margin_bottom = 25.0
modulate_color = Color(0.5, 0.5, 0.5, 0.7)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_slider_bg"]
texture = ExtResource("4_slider_bg")
texture_margin_left = 15.0
texture_margin_top = 15.0
texture_margin_right = 15.0
texture_margin_bottom = 15.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_slider_grabber"]
texture = ExtResource("5_slider_grabber")
texture_margin_left = 10.0
texture_margin_top = 10.0
texture_margin_right = 10.0
texture_margin_bottom = 10.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_panel"]
bg_color = Color(0.1, 0.08, 0.06, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.6, 0.5, 0.3, 0.8)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_line_edit"]
bg_color = Color(0.15, 0.12, 0.08, 0.95)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.5, 0.4, 0.25, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_scroll_bg"]
bg_color = Color(0.2, 0.15, 0.1, 0.3)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[resource]
default_font = ExtResource("1_font")
default_font_size = 18

Button/colors/font_color = Color(0.95, 0.9, 0.8, 1)
Button/colors/font_disabled_color = Color(0.5, 0.4, 0.3, 1)
Button/colors/font_focus_color = Color(1, 0.98, 0.9, 1)
Button/colors/font_hover_color = Color(1, 0.98, 0.9, 1)
Button/colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
Button/colors/font_pressed_color = Color(0.9, 0.85, 0.75, 1)
Button/constants/outline_size = 1
Button/font_sizes/font_size = 20
Button/fonts/font = ExtResource("1_font")
Button/styles/disabled = SubResource("StyleBoxTexture_button_disabled")
Button/styles/focus = SubResource("StyleBoxTexture_button_hover")
Button/styles/hover = SubResource("StyleBoxTexture_button_hover")
Button/styles/normal = SubResource("StyleBoxTexture_button_normal")
Button/styles/pressed = SubResource("StyleBoxTexture_button_pressed")

Label/colors/font_color = Color(0.9, 0.8, 0.6, 1)
Label/colors/font_outline_color = Color(0.1, 0.05, 0.02, 1)
Label/colors/font_shadow_color = Color(0, 0, 0, 0.8)
Label/constants/outline_size = 1
Label/constants/shadow_offset_x = 1
Label/constants/shadow_offset_y = 1
Label/font_sizes/font_size = 18
Label/fonts/font = ExtResource("1_font")

HSlider/colors/grabber_highlight_color = Color(1, 0.9, 0.7, 1)
HSlider/constants/grabber_offset = 0
HSlider/icons/grabber = ExtResource("5_slider_grabber")
HSlider/icons/grabber_highlight = ExtResource("5_slider_grabber")
HSlider/styles/grabber_area = SubResource("StyleBoxTexture_slider_bg")
HSlider/styles/grabber_area_highlight = SubResource("StyleBoxTexture_slider_bg")
HSlider/styles/slider = SubResource("StyleBoxTexture_slider_bg")

VSlider/colors/grabber_highlight_color = Color(1, 0.9, 0.7, 1)
VSlider/constants/grabber_offset = 0
VSlider/icons/grabber = ExtResource("5_slider_grabber")
VSlider/icons/grabber_highlight = ExtResource("5_slider_grabber")
VSlider/styles/grabber_area = SubResource("StyleBoxTexture_slider_bg")
VSlider/styles/grabber_area_highlight = SubResource("StyleBoxTexture_slider_bg")
VSlider/styles/slider = SubResource("StyleBoxTexture_slider_bg")

CheckBox/colors/font_color = Color(0.9, 0.8, 0.6, 1)
CheckBox/colors/font_disabled_color = Color(0.5, 0.4, 0.3, 1)
CheckBox/colors/font_focus_color = Color(1, 0.98, 0.9, 1)
CheckBox/colors/font_hover_color = Color(1, 0.98, 0.9, 1)
CheckBox/colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
CheckBox/colors/font_pressed_color = Color(0.9, 0.85, 0.75, 1)
CheckBox/constants/check_v_offset = 0
CheckBox/constants/outline_size = 1
CheckBox/font_sizes/font_size = 18
CheckBox/fonts/font = ExtResource("1_font")

LineEdit/colors/font_color = Color(0.9, 0.8, 0.6, 1)
LineEdit/colors/font_placeholder_color = Color(0.6, 0.5, 0.4, 0.8)
LineEdit/colors/font_selected_color = Color(1, 1, 1, 1)
LineEdit/colors/selection_color = Color(0.6, 0.5, 0.3, 0.5)
LineEdit/font_sizes/font_size = 18
LineEdit/fonts/font = ExtResource("1_font")
LineEdit/styles/focus = SubResource("StyleBoxFlat_line_edit")
LineEdit/styles/normal = SubResource("StyleBoxFlat_line_edit")
LineEdit/styles/read_only = SubResource("StyleBoxFlat_line_edit")

Panel/styles/panel = SubResource("StyleBoxFlat_panel")

ScrollContainer/styles/panel = SubResource("StyleBoxFlat_scroll_bg")

VBoxContainer/constants/separation = 15
HBoxContainer/constants/separation = 15
MarginContainer/constants/margin_bottom = 20
MarginContainer/constants/margin_left = 20
MarginContainer/constants/margin_right = 20
MarginContainer/constants/margin_top = 20
