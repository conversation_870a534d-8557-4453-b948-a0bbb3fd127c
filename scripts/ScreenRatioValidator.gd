extends Node

# Screen ratio validation system for mobile-first design
# Validates layout consistency across different mobile screen ratios

# Common mobile screen ratios and their characteristics
var screen_ratios = {
	"16:9": {"ratio": 16.0/9.0, "common_resolutions": ["1920x1080", "1280x720", "854x480"]},
	"18:9": {"ratio": 18.0/9.0, "common_resolutions": ["2160x1080", "1440x720"]},
	"19:9": {"ratio": 19.0/9.0, "common_resolutions": ["2280x1080", "1520x720"]},
	"19.5:9": {"ratio": 19.5/9.0, "common_resolutions": ["2340x1080", "1560x720"]},
	"20:9": {"ratio": 20.0/9.0, "common_resolutions": ["2400x1080", "1600x720"]},
	"21:9": {"ratio": 21.0/9.0, "common_resolutions": ["2520x1080", "1680x720"]},
	"4:3": {"ratio": 4.0/3.0, "common_resolutions": ["1024x768", "800x600"]},
	"3:2": {"ratio": 3.0/2.0, "common_resolutions": ["1152x768", "864x576"]}
}

# Test resolutions for validation
var test_resolutions = [
	# Standard mobile portrait
	Vector2(360, 640),   # 16:9 small
	Vector2(375, 667),   # iPhone 6/7/8
	Vector2(375, 812),   # iPhone X/11 Pro (19.5:9)
	Vector2(390, 844),   # iPhone 12/13 mini
	Vector2(393, 851),   # Pixel 5
	Vector2(412, 915),   # Pixel 6
	Vector2(414, 896),   # iPhone 11/XR
	Vector2(428, 926),   # iPhone 12/13 Pro Max
	
	# Tablet portrait
	Vector2(768, 1024),  # iPad (4:3)
	Vector2(800, 1280),  # 16:10 tablet
	Vector2(834, 1194),  # iPad Pro 11"
	Vector2(1024, 1366), # iPad Pro 12.9"
	
	# Landscape variants
	Vector2(640, 360),   # 16:9 landscape
	Vector2(812, 375),   # iPhone X landscape
	Vector2(1024, 768),  # iPad landscape
]

var validation_results = {}

func _ready():
	print("📱 ScreenRatioValidator: Initialized")

func validate_scene_for_all_ratios(scene_path: String) -> Dictionary:
	"""Validate a scene across all common mobile screen ratios"""
	print("🔍 Validating scene: ", scene_path)
	
	var results = {
		"scene_path": scene_path,
		"total_tests": test_resolutions.size(),
		"passed_tests": 0,
		"failed_tests": 0,
		"issues": [],
		"recommendations": [],
		"ratio_results": {}
	}
	
	# Load the scene
	var scene = load(scene_path)
	if not scene:
		results.issues.append("Scene could not be loaded: " + scene_path)
		return results
	
	# Test each resolution
	for resolution in test_resolutions:
		var ratio_result = validate_scene_at_resolution(scene, resolution)
		var ratio_name = get_ratio_name(resolution.x / resolution.y)
		
		results.ratio_results[str(resolution)] = ratio_result
		
		if ratio_result.is_valid:
			results.passed_tests += 1
		else:
			results.failed_tests += 1
			results.issues.append_array(ratio_result.issues)
	
	# Generate recommendations
	results.recommendations = generate_recommendations(results)
	
	validation_results[scene_path] = results
	return results

func validate_scene_at_resolution(scene: PackedScene, resolution: Vector2) -> Dictionary:
	"""Validate a scene at a specific resolution"""
	var result = {
		"resolution": resolution,
		"ratio": resolution.x / resolution.y,
		"ratio_name": get_ratio_name(resolution.x / resolution.y),
		"is_valid": true,
		"issues": [],
		"layout_score": 0.0
	}
	
	# Simulate the resolution
	var instance = scene.instantiate()
	if not instance:
		result.is_valid = false
		result.issues.append("Could not instantiate scene")
		return result
	
	# Add to scene tree temporarily for testing
	get_tree().current_scene.add_child(instance)
	
	# Simulate viewport size
	var viewport = get_viewport()
	var original_size = viewport.get_visible_rect().size
	
	# Test layout at this resolution
	result = validate_layout_at_resolution(instance, resolution, result)
	
	# Cleanup
	instance.queue_free()
	
	return result

func validate_layout_at_resolution(control: Control, resolution: Vector2, result: Dictionary) -> Dictionary:
	"""Validate layout elements at a specific resolution"""
	var score = 100.0
	
	# Check for UI elements going off-screen
	var offscreen_elements = find_offscreen_elements(control, resolution)
	if offscreen_elements.size() > 0:
		result.issues.append("Elements off-screen at " + str(resolution) + ": " + str(offscreen_elements))
		score -= 20.0
		result.is_valid = false
	
	# Check for overlapping elements
	var overlapping_elements = find_overlapping_elements(control)
	if overlapping_elements.size() > 0:
		result.issues.append("Overlapping elements at " + str(resolution) + ": " + str(overlapping_elements))
		score -= 15.0
		result.is_valid = false
	
	# Check for too small touch targets
	var small_targets = find_small_touch_targets(control)
	if small_targets.size() > 0:
		result.issues.append("Small touch targets at " + str(resolution) + ": " + str(small_targets))
		score -= 10.0
		result.is_valid = false
	
	# Check for text readability
	var unreadable_text = find_unreadable_text(control, resolution)
	if unreadable_text.size() > 0:
		result.issues.append("Unreadable text at " + str(resolution) + ": " + str(unreadable_text))
		score -= 15.0
		result.is_valid = false
	
	# Check for proper responsive containers
	if not has_responsive_layout(control):
		result.issues.append("Non-responsive layout detected at " + str(resolution))
		score -= 25.0
		result.is_valid = false
	
	result.layout_score = max(0.0, score)
	return result

func find_offscreen_elements(node: Node, screen_size: Vector2) -> Array:
	"""Find UI elements that go off-screen at given resolution"""
	var offscreen = []
	
	if node is Control:
		var control = node as Control
		var global_rect = control.get_global_rect()
		
		# Check if element extends beyond screen bounds
		if global_rect.position.x < 0 or global_rect.position.y < 0:
			offscreen.append(control.name + " (negative position)")
		
		if global_rect.end.x > screen_size.x or global_rect.end.y > screen_size.y:
			offscreen.append(control.name + " (extends beyond screen)")
	
	# Check children recursively
	for child in node.get_children():
		offscreen.append_array(find_offscreen_elements(child, screen_size))
	
	return offscreen

func find_overlapping_elements(node: Node) -> Array:
	"""Find overlapping UI elements"""
	var overlapping = []
	var controls = []
	
	# Collect all controls
	_collect_controls_recursive(node, controls)
	
	# Check for overlaps
	for i in range(controls.size()):
		for j in range(i + 1, controls.size()):
			var control1 = controls[i]
			var control2 = controls[j]
			
			if control1.get_global_rect().intersects(control2.get_global_rect()):
				overlapping.append(control1.name + " overlaps " + control2.name)
	
	return overlapping

func find_small_touch_targets(node: Node) -> Array:
	"""Find touch targets that are too small for mobile"""
	var small_targets = []
	var min_touch_size = 44  # iOS/Android recommendation
	
	if node is Button:
		var button = node as Button
		var size = button.get_global_rect().size
		if size.x < min_touch_size or size.y < min_touch_size:
			small_targets.append(button.name + " (" + str(size) + ")")
	
	# Check children recursively
	for child in node.get_children():
		small_targets.append_array(find_small_touch_targets(child))
	
	return small_targets

func find_unreadable_text(node: Node, screen_size: Vector2) -> Array:
	"""Find text that may be unreadable at given resolution"""
	var unreadable = []
	var min_font_size = 12  # Minimum readable font size
	
	if node is Label:
		var label = node as Label
		var font_size = label.get_theme_font_size("font_size")
		
		# Adjust minimum based on screen size
		var adjusted_min = min_font_size
		if screen_size.x <= 480:
			adjusted_min = 14  # Larger minimum for small screens
		
		if font_size < adjusted_min:
			unreadable.append(label.name + " (font size: " + str(font_size) + ")")
	
	# Check children recursively
	for child in node.get_children():
		unreadable.append_array(find_unreadable_text(child, screen_size))
	
	return unreadable

func has_responsive_layout(node: Node) -> bool:
	"""Check if layout uses responsive design patterns"""
	# Look for responsive containers
	if node is VBoxContainer or node is HBoxContainer or node is MarginContainer:
		return true
	
	# Look for proper anchor usage
	if node is Control:
		var control = node as Control
		if control.anchors_preset != Control.PRESET_TOP_LEFT:
			return true
	
	# Check children recursively
	for child in node.get_children():
		if has_responsive_layout(child):
			return true
	
	return false

func _collect_controls_recursive(node: Node, controls: Array):
	"""Recursively collect all Control nodes"""
	if node is Control and node.visible:
		controls.append(node)
	
	for child in node.get_children():
		_collect_controls_recursive(child, controls)

func get_ratio_name(ratio: float) -> String:
	"""Get the name of a screen ratio"""
	var tolerance = 0.1
	
	for ratio_name in screen_ratios.keys():
		var target_ratio = screen_ratios[ratio_name].ratio
		if abs(ratio - target_ratio) < tolerance:
			return ratio_name
	
	return "Custom (" + str(ratio).substr(0, 4) + ":1)"

func generate_recommendations(results: Dictionary) -> Array:
	"""Generate recommendations based on validation results"""
	var recommendations = []
	
	if results.failed_tests > 0:
		recommendations.append("Use VBoxContainer and HBoxContainer for responsive layouts")
		recommendations.append("Set proper anchors instead of fixed positioning")
		recommendations.append("Ensure minimum touch target size of 44px")
		recommendations.append("Use MarginContainer for consistent spacing")
		recommendations.append("Test font sizes across different screen densities")
	
	if results.failed_tests > results.total_tests * 0.5:
		recommendations.append("Consider redesigning layout with mobile-first approach")
	
	return recommendations

func validate_common_mobile_ratios(scene_path: String) -> Dictionary:
	"""Quick validation for most common mobile ratios"""
	var common_resolutions = [
		Vector2(375, 667),   # iPhone 6/7/8
		Vector2(375, 812),   # iPhone X (19.5:9)
		Vector2(393, 851),   # Pixel 5 (19:9)
		Vector2(412, 915),   # Pixel 6 (20:9)
	]
	
	var results = {
		"scene_path": scene_path,
		"common_ratio_support": true,
		"supported_ratios": [],
		"unsupported_ratios": [],
		"overall_score": 0.0
	}
	
	var total_score = 0.0
	var scene = load(scene_path)
	
	if not scene:
		results.common_ratio_support = false
		return results
	
	for resolution in common_resolutions:
		var ratio_result = validate_scene_at_resolution(scene, resolution)
		var ratio_name = get_ratio_name(resolution.x / resolution.y)
		
		if ratio_result.is_valid:
			results.supported_ratios.append(ratio_name + " (" + str(resolution) + ")")
		else:
			results.unsupported_ratios.append(ratio_name + " (" + str(resolution) + ")")
			results.common_ratio_support = false
		
		total_score += ratio_result.layout_score
	
	results.overall_score = total_score / common_resolutions.size()
	return results

func print_validation_report(scene_path: String):
	"""Print a detailed validation report"""
	var results = validation_results.get(scene_path)
	if not results:
		print("❌ No validation results found for: ", scene_path)
		return
	
	print("\n📱 MOBILE LAYOUT VALIDATION REPORT")
	print("=" * 50)
	print("Scene: ", scene_path)
	print("Tests: ", results.passed_tests, "/", results.total_tests, " passed")
	print("Success Rate: ", (results.passed_tests * 100.0 / results.total_tests), "%")
	
	if results.issues.size() > 0:
		print("\n❌ ISSUES FOUND:")
		for issue in results.issues:
			print("  • ", issue)
	
	if results.recommendations.size() > 0:
		print("\n💡 RECOMMENDATIONS:")
		for rec in results.recommendations:
			print("  • ", rec)
	
	print("\n📊 RATIO-SPECIFIC RESULTS:")
	for resolution_str in results.ratio_results.keys():
		var ratio_result = results.ratio_results[resolution_str]
		var status = "✅" if ratio_result.is_valid else "❌"
		print("  ", status, " ", resolution_str, " (", ratio_result.ratio_name, ") - Score: ", ratio_result.layout_score, "%")

func get_mobile_readiness_score(scene_path: String) -> float:
	"""Get overall mobile readiness score (0-100)"""
	var results = validation_results.get(scene_path)
	if not results:
		return 0.0
	
	return (results.passed_tests * 100.0) / results.total_tests
