extends Control

# Mobile-first UI references
@onready var main_container = $MainContainer
@onready var title_label = $MainContainer/VBoxContainer/TitleSection/TitleLabel
@onready var scroll_container = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer
@onready var content_vbox = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox
@onready var back_button = $MainContainer/VBoxContainer/ButtonsSection/BackButton

# Content references
@onready var game_title = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/GameTitle
@onready var subtitle = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/Subtitle
@onready var story_title = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/StorySection/StoryTitle
@onready var story_text = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/StorySection/StoryText
@onready var features_title = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/FeaturesSection/FeaturesTitle
@onready var features_text = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/FeaturesSection/FeaturesText
@onready var credits_title = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/CreditsSection/CreditsTitle
@onready var credits_text = $MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/CreditsSection/CreditsText

func _ready():
	setup_mobile_ui()
	setup_content()
	setup_buttons()
	adapt_to_screen_size()

func setup_mobile_ui():
	"""Initialize mobile-first UI"""
	title_label.text = "O HRE"
	
	# Setup mobile scrolling
	scroll_container.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
	scroll_container.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_AUTO
	scroll_container.scroll_deadzone = 0
	scroll_container.follow_focus = true
	
	print("📱 MobileAboutGame: UI nastavené pre mobilné zariadenia")

func setup_content():
	"""Setup game information content"""
	game_title.text = "PREKLIATE DEDIČSTVO"
	subtitle.text = "Van Helsing: Dark Anime Edition"
	
	story_title.text = "PRÍBEH"
	story_text.text = """Ponorte sa do temného sveta Van Helsinga, kde sa stretávajú gotické hrôzy s anime estetikou. Zdedíte záhadný hrad v Transylvánii a odhalíte temné rodinné tajomstvá.

Vaša cesta vás zavedie cez sedem kapitol plných záhad, nadprirodzených stretnutí a morálnych rozhodnutí, ktoré ovplyvnia váš osud."""
	
	features_title.text = "VLASTNOSTI HRY"
	features_text.text = """• 7 kapitol bohatého príbehu
• Interaktívne dialógy s viacerými možnosťami
• Puzzle a logické úlohy
• Atmosférická hudba a zvukové efekty
• Dark anime art štýl
• Mobilná optimalizácia pre pohodlné hranie
• Slovenská lokalizácia"""
	
	credits_title.text = "TVORBA"
	credits_text.text = """Vytvorené v Godot Engine 4.3

Príbeh a dizajn: Van Helsing Team
Programovanie: Godot Community
Hudba: Atmosférické skladby
UI Assets: Dark Templar Theme

Verzia: 1.0.0
© 2024 Van Helsing: Dark Anime Edition"""

func setup_buttons():
	"""Setup navigation buttons"""
	back_button.pressed.connect(_on_back_pressed)

func adapt_to_screen_size():
	"""Mobile-first responsive design adaptation"""
	var viewport = get_viewport()
	if not viewport or not is_instance_valid(viewport):
		print("⚠️ MobileAboutGame: Viewport nie je dostupný")
		return

	var screen_size = viewport.get_visible_rect().size
	var screen_width = screen_size.x
	
	print("📱 AboutGame: Adaptácia na rozlíšenie ", screen_size)

	if screen_width <= 480:  # Small mobile
		adapt_for_small_mobile()
	elif screen_width <= 768:  # Large mobile
		adapt_for_large_mobile()
	else:  # Tablet/Desktop
		adapt_for_tablet_desktop()

func adapt_for_small_mobile():
	"""Optimize for small mobile screens"""
	main_container.add_theme_constant_override("margin_left", 15)
	main_container.add_theme_constant_override("margin_right", 15)
	main_container.add_theme_constant_override("margin_top", 30)
	main_container.add_theme_constant_override("margin_bottom", 30)
	
	# Adjust font sizes for small screens
	title_label.add_theme_font_size_override("font_size", 28)
	game_title.add_theme_font_size_override("font_size", 24)
	subtitle.add_theme_font_size_override("font_size", 18)
	
	# Section titles
	story_title.add_theme_font_size_override("font_size", 20)
	features_title.add_theme_font_size_override("font_size", 20)
	credits_title.add_theme_font_size_override("font_size", 20)
	
	# Content text
	story_text.add_theme_font_size_override("font_size", 14)
	features_text.add_theme_font_size_override("font_size", 14)
	credits_text.add_theme_font_size_override("font_size", 14)
	
	# Button
	back_button.add_theme_font_size_override("font_size", 16)
	back_button.custom_minimum_size = Vector2(100, 45)

func adapt_for_large_mobile():
	"""Optimize for large mobile screens"""
	main_container.add_theme_constant_override("margin_left", 20)
	main_container.add_theme_constant_override("margin_right", 20)
	main_container.add_theme_constant_override("margin_top", 40)
	main_container.add_theme_constant_override("margin_bottom", 40)
	
	# Adjust font sizes for large mobile
	title_label.add_theme_font_size_override("font_size", 32)
	game_title.add_theme_font_size_override("font_size", 26)
	subtitle.add_theme_font_size_override("font_size", 19)
	
	# Section titles
	story_title.add_theme_font_size_override("font_size", 21)
	features_title.add_theme_font_size_override("font_size", 21)
	credits_title.add_theme_font_size_override("font_size", 21)
	
	# Content text
	story_text.add_theme_font_size_override("font_size", 15)
	features_text.add_theme_font_size_override("font_size", 15)
	credits_text.add_theme_font_size_override("font_size", 15)
	
	# Button
	back_button.add_theme_font_size_override("font_size", 17)

func adapt_for_tablet_desktop():
	"""Optimize for tablet and desktop screens"""
	main_container.add_theme_constant_override("margin_left", 40)
	main_container.add_theme_constant_override("margin_right", 40)
	main_container.add_theme_constant_override("margin_top", 60)
	main_container.add_theme_constant_override("margin_bottom", 60)
	
	# Keep default font sizes for larger screens
	title_label.add_theme_font_size_override("font_size", 36)
	game_title.add_theme_font_size_override("font_size", 28)
	subtitle.add_theme_font_size_override("font_size", 20)
	
	# Section titles
	story_title.add_theme_font_size_override("font_size", 22)
	features_title.add_theme_font_size_override("font_size", 22)
	credits_title.add_theme_font_size_override("font_size", 22)
	
	# Content text
	story_text.add_theme_font_size_override("font_size", 16)
	features_text.add_theme_font_size_override("font_size", 16)
	credits_text.add_theme_font_size_override("font_size", 16)
	
	# Button
	back_button.add_theme_font_size_override("font_size", 18)

func _on_back_pressed():
	"""Return to main menu"""
	print("🔙 Returning to main menu from About Game")
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	GameManager.go_to_main_menu()

func _input(event):
	"""Handle input events"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()

func _on_visibility_changed():
	"""Handle visibility changes for mobile optimization"""
	if visible:
		# Reset scroll position when screen becomes visible
		if scroll_container:
			scroll_container.scroll_vertical = 0
		print("📱 AboutGame: Screen visible, scroll reset")
