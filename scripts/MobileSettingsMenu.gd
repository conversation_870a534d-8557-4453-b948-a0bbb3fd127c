extends Control

# Mobile-first UI references
@onready var main_container = $MainContainer
@onready var title_label = $MainContainer/VBoxContainer/TitleSection/TitleLabel
@onready var scroll_container = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer

# Audio controls
@onready var master_volume_slider = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MasterVolumeContainer/MasterVolumeSlider
@onready var master_volume_value = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MasterVolumeContainer/MasterVolumeValue
@onready var music_volume_slider = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MusicVolumeContainer/MusicVolumeSlider
@onready var music_volume_value = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MusicVolumeContainer/MusicVolumeValue
@onready var sfx_volume_slider = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/SFXVolumeContainer/SFXVolumeSlider
@onready var sfx_volume_value = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/SFXVolumeContainer/SFXVolumeValue

# Display controls
@onready var fullscreen_checkbox = $MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/DisplaySection/FullscreenContainer/FullscreenCheckBox

# Buttons
@onready var save_button = $MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox/SaveButton
@onready var back_button = $MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox/BackButton

# Settings data
var settings_data = {
	"master_volume": 80,
	"music_volume": 70,
	"sfx_volume": 85,
	"fullscreen": false
}

func _ready():
	setup_mobile_ui()
	setup_controls()
	load_settings()
	adapt_to_screen_size()

func setup_mobile_ui():
	"""Initialize mobile-first UI"""
	title_label.text = "NASTAVENIA"
	
	# Setup mobile scrolling
	scroll_container.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
	scroll_container.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_AUTO
	scroll_container.scroll_deadzone = 0
	scroll_container.follow_focus = true

func setup_controls():
	"""Setup control signals and initial values"""
	# Audio sliders
	master_volume_slider.value_changed.connect(_on_master_volume_changed)
	music_volume_slider.value_changed.connect(_on_music_volume_changed)
	sfx_volume_slider.value_changed.connect(_on_sfx_volume_changed)
	
	# Display controls
	fullscreen_checkbox.toggled.connect(_on_fullscreen_toggled)
	
	# Buttons
	save_button.pressed.connect(_on_save_pressed)
	back_button.pressed.connect(_on_back_pressed)
	
	# Update initial display values
	_update_volume_displays()

func load_settings():
	"""Load settings from GameManager or config file"""
	if GameManager and GameManager.has_method("get_setting"):
		settings_data.master_volume = GameManager.get_setting("master_volume", 80)
		settings_data.music_volume = GameManager.get_setting("music_volume", 70)
		settings_data.sfx_volume = GameManager.get_setting("sfx_volume", 85)
		settings_data.fullscreen = GameManager.get_setting("fullscreen", false)
	
	# Apply loaded settings to controls
	master_volume_slider.value = settings_data.master_volume
	music_volume_slider.value = settings_data.music_volume
	sfx_volume_slider.value = settings_data.sfx_volume
	fullscreen_checkbox.button_pressed = settings_data.fullscreen
	
	_update_volume_displays()

func save_settings():
	"""Save settings to GameManager or config file"""
	if GameManager and GameManager.has_method("set_setting"):
		GameManager.set_setting("master_volume", settings_data.master_volume)
		GameManager.set_setting("music_volume", settings_data.music_volume)
		GameManager.set_setting("sfx_volume", settings_data.sfx_volume)
		GameManager.set_setting("fullscreen", settings_data.fullscreen)
		GameManager.save_settings()
	
	# Apply audio settings immediately
	if AudioManager:
		AudioManager.set_master_volume(settings_data.master_volume / 100.0)
		AudioManager.set_music_volume(settings_data.music_volume / 100.0)
		AudioManager.set_sfx_volume(settings_data.sfx_volume / 100.0)
	
	# Apply display settings
	if settings_data.fullscreen:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
	else:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)

func adapt_to_screen_size():
	"""Mobile-first responsive design adaptation"""
	var viewport = get_viewport()
	if not viewport or not is_instance_valid(viewport):
		print("⚠️ MobileSettingsMenu: Viewport nie je dostupný")
		return

	var screen_size = viewport.get_visible_rect().size
	var screen_width = screen_size.x
	
	print("📱 SettingsMenu: Adaptácia na rozlíšenie ", screen_size)

	if screen_width <= 480:  # Small mobile
		adapt_for_small_mobile()
	elif screen_width <= 768:  # Large mobile
		adapt_for_large_mobile()
	else:  # Tablet/Desktop
		adapt_for_tablet_desktop()

func adapt_for_small_mobile():
	"""Optimize for small mobile screens"""
	main_container.add_theme_constant_override("margin_left", 15)
	main_container.add_theme_constant_override("margin_right", 15)
	main_container.add_theme_constant_override("margin_top", 30)
	main_container.add_theme_constant_override("margin_bottom", 30)
	
	title_label.add_theme_font_size_override("font_size", 28)
	
	# Smaller font sizes for mobile
	_apply_font_sizes_to_labels(16, 20)
	
	# Larger touch targets for sliders
	_apply_slider_sizes(Vector2(0, 45))

func adapt_for_large_mobile():
	"""Optimize for large mobile screens"""
	main_container.add_theme_constant_override("margin_left", 20)
	main_container.add_theme_constant_override("margin_right", 20)
	main_container.add_theme_constant_override("margin_top", 40)
	main_container.add_theme_constant_override("margin_bottom", 40)
	
	title_label.add_theme_font_size_override("font_size", 32)
	
	_apply_font_sizes_to_labels(18, 22)
	_apply_slider_sizes(Vector2(0, 40))

func adapt_for_tablet_desktop():
	"""Optimize for tablet and desktop screens"""
	main_container.add_theme_constant_override("margin_left", 40)
	main_container.add_theme_constant_override("margin_right", 40)
	main_container.add_theme_constant_override("margin_top", 60)
	main_container.add_theme_constant_override("margin_bottom", 60)
	
	title_label.add_theme_font_size_override("font_size", 36)
	
	_apply_font_sizes_to_labels(18, 24)
	_apply_slider_sizes(Vector2(0, 40))

func _apply_font_sizes_to_labels(normal_size: int, section_size: int):
	"""Apply font sizes to all labels"""
	var labels = get_tree().get_nodes_in_group("settings_labels")
	for label in labels:
		if label.name.ends_with("Label") and not label.name.ends_with("SectionLabel"):
			label.add_theme_font_size_override("font_size", normal_size)
		elif label.name.ends_with("SectionLabel"):
			label.add_theme_font_size_override("font_size", section_size)

func _apply_slider_sizes(size: Vector2):
	"""Apply minimum sizes to sliders for better touch targets"""
	master_volume_slider.custom_minimum_size = size
	music_volume_slider.custom_minimum_size = size
	sfx_volume_slider.custom_minimum_size = size

func _update_volume_displays():
	"""Update volume percentage displays"""
	master_volume_value.text = str(int(master_volume_slider.value)) + "%"
	music_volume_value.text = str(int(music_volume_slider.value)) + "%"
	sfx_volume_value.text = str(int(sfx_volume_slider.value)) + "%"

# Signal handlers
func _on_master_volume_changed(value: float):
	"""Handle master volume change"""
	settings_data.master_volume = int(value)
	master_volume_value.text = str(int(value)) + "%"
	
	# Apply immediately for preview
	if AudioManager:
		AudioManager.set_master_volume(value / 100.0)

func _on_music_volume_changed(value: float):
	"""Handle music volume change"""
	settings_data.music_volume = int(value)
	music_volume_value.text = str(int(value)) + "%"
	
	# Apply immediately for preview
	if AudioManager:
		AudioManager.set_music_volume(value / 100.0)

func _on_sfx_volume_changed(value: float):
	"""Handle SFX volume change"""
	settings_data.sfx_volume = int(value)
	sfx_volume_value.text = str(int(value)) + "%"
	
	# Apply immediately for preview
	if AudioManager:
		AudioManager.set_sfx_volume(value / 100.0)
		# Play test sound
		AudioManager.play_menu_button_sound()

func _on_fullscreen_toggled(pressed: bool):
	"""Handle fullscreen toggle"""
	settings_data.fullscreen = pressed

func _on_save_pressed():
	"""Save settings and return to main menu"""
	print("💾 Saving settings...")
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	save_settings()
	print("✅ Settings saved successfully")
	
	# Return to main menu
	GameManager.go_to_main_menu()

func _on_back_pressed():
	"""Return to main menu without saving"""
	print("🔙 Returning to main menu without saving")
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	GameManager.go_to_main_menu()

func _input(event):
	"""Handle input events"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
