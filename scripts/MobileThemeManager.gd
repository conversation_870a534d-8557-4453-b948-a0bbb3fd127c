extends Node

# Mobile theme configuration singleton
# Handles responsive font sizes, button styles, and spacing for mobile-first design

# Theme resources
var mobile_theme: Theme
var desktop_theme: Theme

# Font size configurations for different screen sizes
var font_configs = {
	"small_mobile": {  # ≤480px
		"title": 28,
		"subtitle": 18,
		"section_title": 20,
		"button": 16,
		"label": 14,
		"description": 14
	},
	"large_mobile": {  # 481-768px
		"title": 32,
		"subtitle": 19,
		"section_title": 21,
		"button": 18,
		"label": 16,
		"description": 15
	},
	"tablet": {  # 769-1024px
		"title": 36,
		"subtitle": 20,
		"section_title": 22,
		"button": 20,
		"label": 18,
		"description": 16
	},
	"desktop": {  # >1024px
		"title": 42,
		"subtitle": 22,
		"section_title": 24,
		"button": 22,
		"label": 20,
		"description": 18
	}
}

# Margin configurations for different screen sizes
var margin_configs = {
	"small_mobile": {
		"main": {"left": 15, "right": 15, "top": 30, "bottom": 30},
		"content": {"left": 10, "right": 10, "top": 15, "bottom": 15}
	},
	"large_mobile": {
		"main": {"left": 20, "right": 20, "top": 40, "bottom": 40},
		"content": {"left": 15, "right": 15, "top": 20, "bottom": 20}
	},
	"tablet": {
		"main": {"left": 40, "right": 40, "top": 60, "bottom": 60},
		"content": {"left": 25, "right": 25, "top": 30, "bottom": 30}
	},
	"desktop": {
		"main": {"left": 80, "right": 80, "top": 80, "bottom": 80},
		"content": {"left": 40, "right": 40, "top": 40, "bottom": 40}
	}
}

# Button size configurations
var button_configs = {
	"small_mobile": {"width": 0.85, "height": 50, "min_touch_size": 44},
	"large_mobile": {"width": 0.8, "height": 55, "min_touch_size": 44},
	"tablet": {"width": 0.7, "height": 60, "min_touch_size": 44},
	"desktop": {"width": 0.6, "height": 65, "min_touch_size": 32}
}

func _ready():
	load_themes()
	print("📱 MobileThemeManager: Initialized")

func load_themes():
	"""Load theme resources"""
	mobile_theme = load("res://themes/MobileTheme.tres")
	desktop_theme = load("res://themes/GothicTheme.tres")
	
	if not mobile_theme:
		print("⚠️ MobileTheme.tres not found, creating fallback")
		mobile_theme = create_fallback_theme()
	
	if not desktop_theme:
		print("⚠️ GothicTheme.tres not found, using mobile theme")
		desktop_theme = mobile_theme

func create_fallback_theme() -> Theme:
	"""Create a fallback theme if mobile theme is not found"""
	var theme = Theme.new()
	var font = load("res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf")
	
	if font:
		theme.default_font = font
		theme.default_font_size = 18
	
	return theme

func get_screen_category() -> String:
	"""Determine screen category based on viewport size"""
	var viewport = Engine.get_main_loop().current_scene.get_viewport()
	if not viewport:
		return "large_mobile"  # Safe fallback
	
	var screen_size = viewport.get_visible_rect().size
	var screen_width = screen_size.x
	
	if screen_width <= 480:
		return "small_mobile"
	elif screen_width <= 768:
		return "large_mobile"
	elif screen_width <= 1024:
		return "tablet"
	else:
		return "desktop"

func apply_mobile_theme_to_control(control: Control):
	"""Apply mobile-optimized theme to a control"""
	if not control:
		return
	
	var screen_category = get_screen_category()
	var font_config = font_configs.get(screen_category, font_configs["large_mobile"])
	var margin_config = margin_configs.get(screen_category, margin_configs["large_mobile"])
	
	# Apply theme
	control.theme = mobile_theme
	
	# Apply responsive margins if it's a MarginContainer
	if control is MarginContainer:
		apply_margins_to_container(control, margin_config["main"])
	
	print("📱 Applied mobile theme to ", control.name, " (", screen_category, ")")

func apply_margins_to_container(container: MarginContainer, margins: Dictionary):
	"""Apply responsive margins to a MarginContainer"""
	container.add_theme_constant_override("margin_left", margins.get("left", 20))
	container.add_theme_constant_override("margin_right", margins.get("right", 20))
	container.add_theme_constant_override("margin_top", margins.get("top", 20))
	container.add_theme_constant_override("margin_bottom", margins.get("bottom", 20))

func apply_responsive_font_sizes(control: Control):
	"""Apply responsive font sizes to all labels and buttons in a control"""
	var screen_category = get_screen_category()
	var font_config = font_configs.get(screen_category, font_configs["large_mobile"])
	
	_apply_fonts_recursive(control, font_config)

func _apply_fonts_recursive(node: Node, font_config: Dictionary):
	"""Recursively apply font sizes to all relevant nodes"""
	if node is Label:
		_apply_label_font_size(node, font_config)
	elif node is Button:
		_apply_button_font_size(node, font_config)
	
	# Process children
	for child in node.get_children():
		_apply_fonts_recursive(child, font_config)

func _apply_label_font_size(label: Label, font_config: Dictionary):
	"""Apply appropriate font size to a label based on its role"""
	var node_name = label.name.to_lower()
	var font_size = font_config["label"]  # Default
	
	if "title" in node_name and not "subtitle" in node_name:
		font_size = font_config["title"]
	elif "subtitle" in node_name:
		font_size = font_config["subtitle"]
	elif "section" in node_name or "header" in node_name:
		font_size = font_config["section_title"]
	elif "description" in node_name or "text" in node_name:
		font_size = font_config["description"]
	
	label.add_theme_font_size_override("font_size", font_size)

func _apply_button_font_size(button: Button, font_config: Dictionary):
	"""Apply appropriate font size to a button"""
	button.add_theme_font_size_override("font_size", font_config["button"])

func apply_responsive_button_sizes(control: Control):
	"""Apply responsive button sizes to all buttons in a control"""
	var screen_category = get_screen_category()
	var button_config = button_configs.get(screen_category, button_configs["large_mobile"])
	var viewport = Engine.get_main_loop().current_scene.get_viewport()
	
	if not viewport:
		return
	
	var screen_width = viewport.get_visible_rect().size.x
	var button_width = screen_width * button_config["width"]
	var button_height = button_config["height"]
	var min_touch_size = button_config["min_touch_size"]
	
	# Ensure minimum touch target size
	button_width = max(button_width, min_touch_size)
	button_height = max(button_height, min_touch_size)
	
	_apply_button_sizes_recursive(control, Vector2(button_width, button_height))

func _apply_button_sizes_recursive(node: Node, size: Vector2):
	"""Recursively apply button sizes to all buttons"""
	if node is Button:
		node.custom_minimum_size = size
	
	# Process children
	for child in node.get_children():
		_apply_button_sizes_recursive(child, size)

func setup_mobile_scrolling(scroll_container: ScrollContainer):
	"""Setup optimal scrolling for mobile devices"""
	if not scroll_container:
		return
	
	scroll_container.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
	scroll_container.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_AUTO
	scroll_container.scroll_deadzone = 0
	scroll_container.follow_focus = true
	
	print("📱 Mobile scrolling configured for ", scroll_container.name)

func validate_mobile_layout(control: Control) -> Dictionary:
	"""Validate mobile layout and return recommendations"""
	var issues = []
	var recommendations = []
	
	# Check for fixed positioning
	if _has_fixed_positioning(control):
		issues.append("Fixed positioning detected")
		recommendations.append("Use anchors and containers instead of fixed offsets")
	
	# Check for small touch targets
	var small_buttons = _find_small_buttons(control)
	if small_buttons.size() > 0:
		issues.append("Small touch targets found: " + str(small_buttons.size()))
		recommendations.append("Ensure buttons are at least 44px for mobile")
	
	# Check for missing responsive containers
	if not _has_responsive_containers(control):
		issues.append("Missing responsive containers")
		recommendations.append("Use VBoxContainer, HBoxContainer, and MarginContainer")
	
	return {
		"issues": issues,
		"recommendations": recommendations,
		"is_mobile_ready": issues.size() == 0
	}

func _has_fixed_positioning(node: Node) -> bool:
	"""Check if node tree has fixed positioning issues"""
	if node is Control:
		var control = node as Control
		if control.anchors_preset == Control.PRESET_CENTER_LEFT or control.anchors_preset == Control.PRESET_CENTER_RIGHT:
			if control.position != Vector2.ZERO:
				return true
	
	for child in node.get_children():
		if _has_fixed_positioning(child):
			return true
	
	return false

func _find_small_buttons(node: Node) -> Array:
	"""Find buttons that are too small for mobile"""
	var small_buttons = []
	
	if node is Button:
		var button = node as Button
		var size = button.custom_minimum_size
		if size.x > 0 and size.x < 44 or size.y > 0 and size.y < 44:
			small_buttons.append(button.name)
	
	for child in node.get_children():
		small_buttons.append_array(_find_small_buttons(child))
	
	return small_buttons

func _has_responsive_containers(node: Node) -> bool:
	"""Check if node tree uses responsive containers"""
	if node is VBoxContainer or node is HBoxContainer or node is MarginContainer:
		return true
	
	for child in node.get_children():
		if _has_responsive_containers(child):
			return true
	
	return false

func get_optimal_font_size(base_size: int, element_type: String = "label") -> int:
	"""Get optimal font size for current screen"""
	var screen_category = get_screen_category()
	var font_config = font_configs.get(screen_category, font_configs["large_mobile"])
	
	return font_config.get(element_type, base_size)

func get_optimal_margins() -> Dictionary:
	"""Get optimal margins for current screen"""
	var screen_category = get_screen_category()
	return margin_configs.get(screen_category, margin_configs["large_mobile"])["main"]

func print_mobile_debug_info():
	"""Print debug information about current mobile configuration"""
	var screen_category = get_screen_category()
	var viewport = Engine.get_main_loop().current_scene.get_viewport()
	
	if viewport:
		var screen_size = viewport.get_visible_rect().size
		print("📱 Mobile Debug Info:")
		print("  Screen size: ", screen_size)
		print("  Category: ", screen_category)
		print("  Font config: ", font_configs[screen_category])
		print("  Margin config: ", margin_configs[screen_category])
		print("  Button config: ", button_configs[screen_category])
