extends Control

# Mobile-first UI references
@onready var main_container = $MainContainer
@onready var title_label = $MainContainer/VBoxContainer/TitleSection/TitleLabel
@onready var chapters_list = $MainContainer/VBoxContainer/ContentSection/ChaptersList
@onready var chapters_vbox = $MainContainer/VBoxContainer/ContentSection/ChaptersList/ChaptersVBox
@onready var chapter_info = $MainContainer/VBoxContainer/ContentSection/ChapterInfo
@onready var chapter_image = $MainContainer/VBoxContainer/ContentSection/ChapterInfo/InfoContainer/ChapterImage
@onready var chapter_title = $MainContainer/VBoxContainer/ContentSection/ChapterInfo/InfoContainer/ChapterTitle
@onready var chapter_description = $MainContainer/VBoxContainer/ContentSection/ChapterInfo/InfoContainer/ChapterDescription
@onready var play_button = $MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox/PlayButton
@onready var back_button = $MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox/BackButton

# Chapter data
var chapters_data = [
	{
		"id": 1,
		"title": "Kapitola 1: Dedičstvo",
		"description": "Van Helsing dostáva list o dedičstve po svojom strýkovi. Cesta do Transylvánie sa začína...",
		"image": "res://assets/Kapitoly_VISUALS/Kapitola_1.png",
		"unlocked": true
	},
	{
		"id": 2,
		"title": "Kapitola 2: Prvé stopy",
		"description": "Príchod do dedičného hradu odhaľuje prvé znepokojujúce stopy temného tajomstva...",
		"image": "res://assets/Kapitoly_VISUALS/Kapitola_2.png",
		"unlocked": false
	},
	{
		"id": 3,
		"title": "Kapitola 3: Temné tajomstvo",
		"description": "Hlbšie v hrade Van Helsing objavuje pravdu o svojom dedičstve a temných silách...",
		"image": "res://assets/Kapitoly_VISUALS/Kapitola_3.png",
		"unlocked": false
	}
]

var selected_chapter = 1
var chapter_buttons = []

func _ready():
	setup_mobile_ui()
	setup_chapters()
	setup_buttons()
	adapt_to_screen_size()
	select_chapter(1)

func setup_mobile_ui():
	"""Initialize mobile-first UI"""
	title_label.text = "KAPITOLY"
	
	# Setup mobile scrolling
	chapters_list.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
	chapters_list.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_AUTO
	chapters_list.scroll_deadzone = 0

func setup_chapters():
	"""Create chapter buttons dynamically"""
	# Clear existing buttons (except the template ones)
	for child in chapters_vbox.get_children():
		if child.name.begins_with("Chapter"):
			child.queue_free()
	
	# Create new chapter buttons
	for i in range(chapters_data.size()):
		var chapter = chapters_data[i]
		var button = create_chapter_button(chapter, i)
		chapters_vbox.add_child(button)
		chapter_buttons.append(button)

func create_chapter_button(chapter_data: Dictionary, index: int) -> Button:
	"""Create a mobile-optimized chapter button"""
	var button = Button.new()
	button.name = "Chapter" + str(chapter_data.id)
	button.layout_mode = 2
	button.custom_minimum_size = Vector2(0, 60)
	button.text = chapter_data.title
	
	# Apply mobile-friendly styling
	var font = load("res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf")
	button.add_theme_font_override("font", font)
	button.add_theme_font_size_override("font_size", 18)
	button.add_theme_color_override("font_color", Color(0.95, 0.9, 0.8, 1))
	
	# Style based on unlock status
	if chapter_data.unlocked:
		button.add_theme_color_override("font_color", Color(0.95, 0.9, 0.8, 1))
		button.disabled = false
	else:
		button.add_theme_color_override("font_color", Color(0.5, 0.4, 0.3, 1))
		button.disabled = true
	
	# Connect signal
	button.pressed.connect(_on_chapter_selected.bind(chapter_data.id))
	
	return button

func setup_buttons():
	"""Setup navigation buttons"""
	play_button.pressed.connect(_on_play_pressed)
	back_button.pressed.connect(_on_back_pressed)

func adapt_to_screen_size():
	"""Mobile-first responsive design adaptation"""
	var viewport = get_viewport()
	if not viewport or not is_instance_valid(viewport):
		print("⚠️ MobileChaptersMenu: Viewport nie je dostupný")
		return

	var screen_size = viewport.get_visible_rect().size
	var screen_width = screen_size.x
	var screen_height = screen_size.y
	var aspect_ratio = screen_width / screen_height
	
	print("📱 ChaptersMenu: Adaptácia na rozlíšenie ", screen_size, " (pomer: ", aspect_ratio, ")")

	if screen_width <= 480:  # Small mobile
		adapt_for_small_mobile()
	elif screen_width <= 768:  # Large mobile
		adapt_for_large_mobile()
	else:  # Tablet/Desktop
		adapt_for_tablet_desktop()

func adapt_for_small_mobile():
	"""Optimize for small mobile screens"""
	main_container.add_theme_constant_override("margin_left", 15)
	main_container.add_theme_constant_override("margin_right", 15)
	main_container.add_theme_constant_override("margin_top", 30)
	main_container.add_theme_constant_override("margin_bottom", 30)
	
	title_label.add_theme_font_size_override("font_size", 28)
	chapter_title.add_theme_font_size_override("font_size", 20)
	chapter_description.add_theme_font_size_override("font_size", 14)
	
	# Smaller buttons for mobile
	for button in chapter_buttons:
		button.custom_minimum_size = Vector2(0, 55)
		button.add_theme_font_size_override("font_size", 16)
	
	play_button.add_theme_font_size_override("font_size", 16)
	back_button.add_theme_font_size_override("font_size", 16)

func adapt_for_large_mobile():
	"""Optimize for large mobile screens"""
	main_container.add_theme_constant_override("margin_left", 20)
	main_container.add_theme_constant_override("margin_right", 20)
	main_container.add_theme_constant_override("margin_top", 40)
	main_container.add_theme_constant_override("margin_bottom", 40)
	
	title_label.add_theme_font_size_override("font_size", 32)
	chapter_title.add_theme_font_size_override("font_size", 22)
	chapter_description.add_theme_font_size_override("font_size", 15)

func adapt_for_tablet_desktop():
	"""Optimize for tablet and desktop screens"""
	main_container.add_theme_constant_override("margin_left", 40)
	main_container.add_theme_constant_override("margin_right", 40)
	main_container.add_theme_constant_override("margin_top", 60)
	main_container.add_theme_constant_override("margin_bottom", 60)
	
	title_label.add_theme_font_size_override("font_size", 36)
	chapter_title.add_theme_font_size_override("font_size", 24)
	chapter_description.add_theme_font_size_override("font_size", 16)

func select_chapter(chapter_id: int):
	"""Select and display chapter information"""
	selected_chapter = chapter_id
	
	# Find chapter data
	var chapter_data = null
	for chapter in chapters_data:
		if chapter.id == chapter_id:
			chapter_data = chapter
			break
	
	if not chapter_data:
		print("⚠️ Chapter not found: ", chapter_id)
		return
	
	# Update chapter info display
	chapter_title.text = chapter_data.title
	chapter_description.text = chapter_data.description
	
	# Load chapter image
	if FileAccess.file_exists(chapter_data.image):
		var texture = load(chapter_data.image)
		if texture:
			chapter_image.texture = texture
	
	# Update play button state
	play_button.disabled = not chapter_data.unlocked
	
	print("📖 Selected chapter: ", chapter_data.title)

func _on_chapter_selected(chapter_id: int):
	"""Handle chapter selection"""
	print("📚 Chapter selected: ", chapter_id)
	if AudioManager:
		AudioManager.play_menu_button_sound()
	select_chapter(chapter_id)

func _on_play_pressed():
	"""Start selected chapter"""
	print("▶️ Playing chapter: ", selected_chapter)
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	# Start the selected chapter
	GameManager.start_chapter(selected_chapter)

func _on_back_pressed():
	"""Return to main menu"""
	print("🔙 Returning to main menu")
	if AudioManager:
		AudioManager.play_menu_button_sound()
	
	GameManager.go_to_main_menu()

func _input(event):
	"""Handle input events"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
