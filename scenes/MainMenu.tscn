[gd_scene load_steps=8 format=3 uid="uid://bx8vn7qkqxqxq"]

[ext_resource type="Script" uid="uid://dcflxi1ai8tpb" path="res://scripts/MainMenu.gd" id="1_main"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_background"]
[ext_resource type="Texture2D" uid="uid://bsynv0a2bikii" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Overlay Title Game.png" id="3_title_overlay"]
[ext_resource type="Texture2D" uid="uid://c04urphevw0x6" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Normal.png" id="4_button_normal"]
[ext_resource type="Texture2D" uid="uid://blse0wie8krge" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/Button Hover.png" id="5_button_hover"]
[ext_resource type="FontFile" uid="uid://bqhqhqhqhqhqh" path="res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf" id="6_font"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_normal"]
texture = ExtResource("4_button_normal")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_background")
expand_mode = 1
stretch_mode = 1

[node name="MainContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/margin_left = 40
theme_override_constants/margin_top = 60
theme_override_constants/margin_right = 40
theme_override_constants/margin_bottom = 60

[node name="VBoxContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="TopSpacer" type="Control" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
custom_minimum_size = Vector2(0, 80)

[node name="TitleSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="TitleVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="TitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 42
text = "PREKLIATE DEDIČSTVO"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SubtitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection/TitleVBox"]
layout_mode = 2
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 0.9)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 20
text = "Van Helsing: Dark Anime Edition"
horizontal_alignment = 1
vertical_alignment = 1

[node name="MiddleSpacer" type="Control" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
custom_minimum_size = Vector2(0, 40)

[node name="MenuSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="MenuVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/MenuSection"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="ButtonsCenterContainer" type="CenterContainer" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox"]
layout_mode = 2

[node name="ButtonsContainer" type="VBoxContainer" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer"]
layout_mode = 2
theme_override_constants/separation = 12
[node name="NovaHraButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
size_flags_horizontal = 4
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
theme_override_styles/normal = SubResource("StyleBoxTexture_button_normal")
text = "NOVÁ HRA"

[node name="PokracovatButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
size_flags_horizontal = 4
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
theme_override_styles/normal = SubResource("StyleBoxTexture_button_normal")
text = "POKRAČOVAŤ"

[node name="KapitolyButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
size_flags_horizontal = 4
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
theme_override_styles/normal = SubResource("StyleBoxTexture_button_normal")
text = "KAPITOLY"

[node name="NastaveniaButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
size_flags_horizontal = 4
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
theme_override_styles/normal = SubResource("StyleBoxTexture_button_normal")
text = "NASTAVENIA"

[node name="OHreButton" type="Button" parent="MainContainer/VBoxContainer/MenuSection/MenuVBox/ButtonsCenterContainer/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
size_flags_horizontal = 4
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_colors/font_hover_color = Color(1, 0.98, 0.9, 1)
theme_override_colors/font_outline_color = Color(0.15, 0.08, 0.03, 1)
theme_override_constants/outline_size = 1
theme_override_styles/normal = SubResource("StyleBoxTexture_button_normal")
text = "O HRE"

[node name="BottomSpacer" type="Control" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
[node name="VersionLabel" type="Label" parent="MainContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.7, 0.6, 0.4, 0.8)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 16
text = "verzia 1.0.0"
horizontal_alignment = 1
vertical_alignment = 1
