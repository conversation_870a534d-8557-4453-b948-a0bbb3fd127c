[gd_scene load_steps=6 format=3 uid="uid://mobile_about_game"]

[ext_resource type="Script" path="res://scripts/MobileAboutGame.gd" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_bg"]
[ext_resource type="Texture2D" uid="uid://cpwk244fogft1" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="3_main_panel"]
[ext_resource type="Texture2D" uid="uid://h4fgxwlt7858" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Normal.png" id="4_button"]
[ext_resource type="FontFile" path="res://fonts/<PERSON><PERSON><PERSON>,Co<PERSON><PERSON><PERSON>_<PERSON>,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf" id="5_font"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button"]
texture = ExtResource("4_button")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[node name="MobileAboutGame" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 1

[node name="MainContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 40
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 40

[node name="VBoxContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="TitleSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="TitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 36
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
text = "O HRE"
horizontal_alignment = 1

[node name="ContentPanel" type="NinePatchRect" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
texture = ExtResource("3_main_panel")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="ScrollContainer" type="ScrollContainer" parent="MainContainer/VBoxContainer/ContentPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 35.0
offset_top = 35.0
offset_right = -35.0
offset_bottom = -35.0
horizontal_scroll_mode = 0
vertical_scroll_mode = 2

[node name="ContentVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 20

[node name="GameTitle" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 28
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "PREKLIATE DEDIČSTVO"
horizontal_alignment = 1

[node name="Subtitle" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 20
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Van Helsing: Dark Anime Edition"
horizontal_alignment = 1

[node name="Separator1" type="HSeparator" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="StorySection" type="VBoxContainer" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="StoryTitle" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/StorySection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 22
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "PRÍBEH"
horizontal_alignment = 1

[node name="StoryText" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/StorySection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 16
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Ponorte sa do temného sveta Van Helsinga, kde sa stretávajú gotické hrôzy s anime estetikou. Zdedíte záhadný hrad v Transylvánii a odhalíte temné rodinné tajomstvá.

Vaša cesta vás zavedie cez sedem kapitol plných záhad, nadprirodzených stretnutí a morálnych rozhodnutí, ktoré ovplyvnia váš osud."
autowrap_mode = 3

[node name="Separator2" type="HSeparator" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="FeaturesSection" type="VBoxContainer" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="FeaturesTitle" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/FeaturesSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 22
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "VLASTNOSTI HRY"
horizontal_alignment = 1

[node name="FeaturesText" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/FeaturesSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 16
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "• 7 kapitol bohatého príbehu
• Interaktívne dialógy s viacerými možnosťami
• Puzzle a logické úlohy
• Atmosférická hudba a zvukové efekty
• Dark anime art štýl
• Mobilná optimalizácia pre pohodlné hranie
• Slovenská lokalizácia"
autowrap_mode = 3

[node name="Separator3" type="HSeparator" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="CreditsSection" type="VBoxContainer" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="CreditsTitle" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/CreditsSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 22
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "TVORBA"
horizontal_alignment = 1

[node name="CreditsText" type="Label" parent="MainContainer/VBoxContainer/ContentPanel/ScrollContainer/ContentVBox/CreditsSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 16
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Vytvorené v Godot Engine 4.3

Príbeh a dizajn: Van Helsing Team
Programovanie: Godot Community
Hudba: Atmosférické skladby
UI Assets: Dark Templar Theme

Verzia: 1.0.0
© 2024 Van Helsing: Dark Anime Edition"
autowrap_mode = 3

[node name="ButtonsSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="BackButton" type="Button" parent="MainContainer/VBoxContainer/ButtonsSection"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
theme_override_fonts/font = ExtResource("5_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "SPÄŤ"
