[gd_scene load_steps=12 format=3 uid="uid://mobile_chapters_menu"]

[ext_resource type="Script" path="res://scripts/MobileChaptersMenu.gd" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_bg"]
[ext_resource type="Texture2D" uid="uid://cpwk244fogft1" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="3_main_panel"]
[ext_resource type="Texture2D" uid="uid://bfoshjpk7gun5" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Panel4.png" id="4_chapter_panel"]
[ext_resource type="Texture2D" uid="uid://h4fgxwlt7858" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Normal.png" id="5_button"]
[ext_resource type="Texture2D" uid="uid://6hx86f2x1kth" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Hover.png" id="6_button_hover"]
[ext_resource type="FontFile" path="res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf" id="7_font"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_1.png" id="8_ch1"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_2.png" id="9_ch2"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_3.png" id="10_ch3"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button"]
texture = ExtResource("5_button")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[node name="MobileChaptersMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 1

[node name="MainContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 40
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 40

[node name="VBoxContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="TitleSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="TitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 36
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
text = "KAPITOLY"
horizontal_alignment = 1

[node name="ContentSection" type="HBoxContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 15

[node name="ChaptersList" type="ScrollContainer" parent="MainContainer/VBoxContainer/ContentSection"]
layout_mode = 2
size_flags_horizontal = 3
horizontal_scroll_mode = 0
vertical_scroll_mode = 2

[node name="ChaptersVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/ContentSection/ChaptersList"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 10

[node name="Chapter1" type="Button" parent="MainContainer/VBoxContainer/ContentSection/ChaptersList/ChaptersVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 20
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "Kapitola 1: Dedičstvo"

[node name="Chapter2" type="Button" parent="MainContainer/VBoxContainer/ContentSection/ChaptersList/ChaptersVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 20
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "Kapitola 2: Prvé stopy"

[node name="Chapter3" type="Button" parent="MainContainer/VBoxContainer/ContentSection/ChaptersList/ChaptersVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 20
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "Kapitola 3: Temné tajomstvo"

[node name="ChapterInfo" type="NinePatchRect" parent="MainContainer/VBoxContainer/ContentSection"]
layout_mode = 2
size_flags_horizontal = 3
texture = ExtResource("4_chapter_panel")
patch_margin_left = 20
patch_margin_top = 20
patch_margin_right = 20
patch_margin_bottom = 20

[node name="InfoContainer" type="VBoxContainer" parent="MainContainer/VBoxContainer/ContentSection/ChapterInfo"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
theme_override_constants/separation = 15

[node name="ChapterImage" type="TextureRect" parent="MainContainer/VBoxContainer/ContentSection/ChapterInfo/InfoContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 200)
texture = ExtResource("8_ch1")
expand_mode = 1
stretch_mode = 4

[node name="ChapterTitle" type="Label" parent="MainContainer/VBoxContainer/ContentSection/ChapterInfo/InfoContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "Kapitola 1: Dedičstvo"
horizontal_alignment = 1

[node name="ChapterDescription" type="Label" parent="MainContainer/VBoxContainer/ContentSection/ChapterInfo/InfoContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 16
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Van Helsing dostáva list o dedičstve po svojom strýkovi. Cesta do Transylvánie sa začína..."
autowrap_mode = 3

[node name="ButtonsSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="ButtonsHBox" type="HBoxContainer" parent="MainContainer/VBoxContainer/ButtonsSection"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="PlayButton" type="Button" parent="MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "HRAŤ"

[node name="BackButton" type="Button" parent="MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
theme_override_fonts/font = ExtResource("7_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "SPÄŤ"
