[gd_scene load_steps=8 format=3 uid="uid://mobile_settings_menu"]

[ext_resource type="Script" path="res://scripts/MobileSettingsMenu.gd" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_bg"]
[ext_resource type="Texture2D" uid="uid://cpwk244fogft1" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="3_main_panel"]
[ext_resource type="Texture2D" uid="uid://bfoshjpk7gun5" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Panel4.png" id="4_settings_panel"]
[ext_resource type="Texture2D" uid="uid://h4fgxwlt7858" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Normal.png" id="5_button"]
[ext_resource type="FontFile" path="res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf" id="6_font"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button"]
texture = ExtResource("5_button")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[node name="MobileSettingsMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 1

[node name="MainContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 40
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 40

[node name="VBoxContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="TitleSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="TitleLabel" type="Label" parent="MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 36
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
text = "NASTAVENIA"
horizontal_alignment = 1

[node name="SettingsPanel" type="NinePatchRect" parent="MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
texture = ExtResource("4_settings_panel")
patch_margin_left = 20
patch_margin_top = 20
patch_margin_right = 20
patch_margin_bottom = 20

[node name="ScrollContainer" type="ScrollContainer" parent="MainContainer/VBoxContainer/SettingsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 25.0
offset_top = 25.0
offset_right = -25.0
offset_bottom = -25.0
horizontal_scroll_mode = 0
vertical_scroll_mode = 2

[node name="SettingsVBox" type="VBoxContainer" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 20

[node name="AudioSection" type="VBoxContainer" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="AudioLabel" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "ZVUK"
horizontal_alignment = 1

[node name="MasterVolumeContainer" type="HBoxContainer" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="MasterVolumeLabel" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MasterVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Hlavná hlasitosť:"
vertical_alignment = 1

[node name="MasterVolumeSlider" type="HSlider" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MasterVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(0, 40)
min_value = 0.0
max_value = 100.0
step = 1.0
value = 80.0

[node name="MasterVolumeValue" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MasterVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(50, 0)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "80%"
horizontal_alignment = 1
vertical_alignment = 1

[node name="MusicVolumeContainer" type="HBoxContainer" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="MusicVolumeLabel" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MusicVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Hudba:"
vertical_alignment = 1

[node name="MusicVolumeSlider" type="HSlider" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MusicVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(0, 40)
min_value = 0.0
max_value = 100.0
step = 1.0
value = 70.0

[node name="MusicVolumeValue" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/MusicVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(50, 0)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "70%"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SFXVolumeContainer" type="HBoxContainer" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="SFXVolumeLabel" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/SFXVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Zvukové efekty:"
vertical_alignment = 1

[node name="SFXVolumeSlider" type="HSlider" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/SFXVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(0, 40)
min_value = 0.0
max_value = 100.0
step = 1.0
value = 85.0

[node name="SFXVolumeValue" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/AudioSection/SFXVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(50, 0)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "85%"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Separator1" type="HSeparator" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="DisplaySection" type="VBoxContainer" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="DisplayLabel" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/DisplaySection"]
layout_mode = 2
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 24
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
text = "ZOBRAZENIE"
horizontal_alignment = 1

[node name="FullscreenContainer" type="HBoxContainer" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/DisplaySection"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="FullscreenLabel" type="Label" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/DisplaySection/FullscreenContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
text = "Celá obrazovka:"
vertical_alignment = 1

[node name="FullscreenCheckBox" type="CheckBox" parent="MainContainer/VBoxContainer/SettingsPanel/ScrollContainer/SettingsVBox/DisplaySection/FullscreenContainer"]
layout_mode = 2
custom_minimum_size = Vector2(40, 40)

[node name="ButtonsSection" type="CenterContainer" parent="MainContainer/VBoxContainer"]
layout_mode = 2

[node name="ButtonsHBox" type="HBoxContainer" parent="MainContainer/VBoxContainer/ButtonsSection"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="SaveButton" type="Button" parent="MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "ULOŽIŤ"

[node name="BackButton" type="Button" parent="MainContainer/VBoxContainer/ButtonsSection/ButtonsHBox"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.95, 0.9, 0.8, 1)
theme_override_styles/normal = SubResource("StyleBoxTexture_button")
text = "SPÄŤ"
